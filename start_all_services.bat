@echo off
echo Starting all Django microservices...
echo ====================================

echo Starting Authentication Service on port 8001...
start "Authentication Service - Port 8001" cmd /k "call env\Scripts\activate.bat && cd backend\authentication && python manage.py runserver 8001"

timeout /t 2 /nobreak >nul

echo Starting Assets Service on port 8002...
start "Assets Service - Port 8002" cmd /k "call env\Scripts\activate.bat && cd backend\assets && python manage.py runserver 8002"

timeout /t 2 /nobreak >nul

echo Starting Accessories Service on port 8004...
start "Accessories Service - Port 8004" cmd /k "call env\Scripts\activate.bat && cd backend\accessories && python manage.py runserver 8004"

timeout /t 2 /nobreak >nul

echo Starting Consumables Service on port 8005...
start "Consumables Service - Port 8005" cmd /k "call env\Scripts\activate.bat && cd backend\consumables && python manage.py runserver 8005"

echo.
echo All services have been started in separate windows!
echo Contexts service is already running on port 8003 in your main terminal.
echo.
echo Services:
echo - Authentication: http://127.0.0.1:8001
echo - Assets: http://127.0.0.1:8002  
echo - Contexts: http://127.0.0.1:8003 (already running)
echo - Accessories: http://127.0.0.1:8004
echo - Consumables: http://127.0.0.1:8005
echo.
pause
