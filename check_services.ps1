Write-Host "Checking Django Services Status..." -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$services = @(
    @{Name="Authentication"; Port=8001; URL="http://127.0.0.1:8001"},
    @{Name="Assets"; Port=8002; URL="http://127.0.0.1:8002"},
    @{Name="Contexts"; Port=8003; URL="http://127.0.0.1:8003"},
    @{Name="Accessories"; Port=8004; URL="http://127.0.0.1:8004"},
    @{Name="Consumables"; Port=8005; URL="http://127.0.0.1:8005"}
)

foreach ($service in $services) {
    Write-Host "Checking $($service.Name) service on port $($service.Port)..." -NoNewline
    try {
        $response = Invoke-WebRequest -Uri $service.URL -TimeoutSec 3 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host " ✅ RUNNING" -ForegroundColor Green
        } else {
            Write-Host " ⚠️  HTTP $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host " ❌ NOT ACCESSIBLE" -ForegroundColor Red
    }
}

Write-Host "`nService Status Check Complete!" -ForegroundColor Green
